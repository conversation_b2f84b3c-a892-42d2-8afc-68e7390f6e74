/// Model class representing a job posting
class JobPosting {
  final String title;
  final String organization;
  final String employmentType;
  final String jobLocation;
  final String baseSalary;
  final String closingDate;
  final String image;
  final String applyUrl;
  final String description;

  const JobPosting({
    required this.title,
    required this.organization,
    required this.employmentType,
    required this.jobLocation,
    required this.baseSalary,
    required this.closingDate,
    required this.image,
    required this.applyUrl,
    required this.description,
  });

  /// Creates a JobPosting from a JSON map
  factory JobPosting.fromJson(Map<String, dynamic> json) {
    return JobPosting(
      title: json['title'] ?? '',
      organization: json['organization'] ?? '',
      employmentType: json['employmentType'] ?? '',
      jobLocation: json['jobLocation'] ?? '',
      baseSalary: json['baseSalary'] ?? '',
      closingDate: json['closingDate'] ?? '',
      image: json['image'] ?? '',
      applyUrl: json['applyUrl'] ?? '',
      description: json['description'] ?? '',
    );
  }

  /// Converts the JobPosting to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'organization': organization,
      'employmentType': employmentType,
      'jobLocation': jobLocation,
      'baseSalary': baseSalary,
      'closingDate': closingDate,
      'image': image,
      'applyUrl': applyUrl,
      'description': description,
    };
  }

  /// Helper method to get formatted location
  String get formattedLocation {
    // Clean up location string by removing extra commas and spaces
    return jobLocation
        .split(',')
        .map((part) => part.trim())
        .where((part) => part.isNotEmpty)
        .join(', ');
  }

  /// Helper method to check if closing date is soon (within 7 days)
  bool get isClosingSoon {
    try {
      final parts = closingDate.split('-');
      if (parts.length == 3) {
        final day = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        final closingDateTime = DateTime(year, month, day);
        final now = DateTime.now();
        final difference = closingDateTime.difference(now).inDays;
        return difference <= 7 && difference >= 0;
      }
    } catch (e) {
      // If date parsing fails, assume not closing soon
    }
    return false;
  }

  /// Helper method to get a short description (first 150 characters)
  String get shortDescription {
    if (description.length <= 150) {
      return description;
    }
    return '${description.substring(0, 147)}...';
  }

  @override
  String toString() {
    return 'JobPosting(title: $title, organization: $organization, location: $jobLocation)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JobPosting &&
        other.title == title &&
        other.organization == organization &&
        other.applyUrl == applyUrl;
  }

  @override
  int get hashCode {
    return title.hashCode ^ organization.hashCode ^ applyUrl.hashCode;
  }
}
