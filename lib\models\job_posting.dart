/// Model class representing a job posting
class JobPosting {
  final String title;
  final String organization;
  final String employmentType;
  final String jobLocation;
  final String baseSalary;
  final String closingDate;
  final String image;
  final String applyUrl;
  final String description;

  const JobPosting({
    required this.title,
    required this.organization,
    required this.employmentType,
    required this.jobLocation,
    required this.baseSalary,
    required this.closingDate,
    required this.image,
    required this.applyUrl,
    required this.description,
  });

  /// Creates a JobPosting from a JSON map
  factory JobPosting.fromJson(Map<String, dynamic> json) {
    return JobPosting(
      title: json['title'] ?? '',
      organization: json['organization'] ?? '',
      employmentType: json['employmentType'] ?? '',
      jobLocation: json['jobLocation'] ?? '',
      baseSalary: json['baseSalary'] ?? '',
      closingDate: json['closingDate'] ?? '',
      image: json['image'] ?? '',
      applyUrl: json['applyUrl'] ?? '',
      description: json['description'] ?? '',
    );
  }

  /// Converts the JobPosting to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'organization': organization,
      'employmentType': employmentType,
      'jobLocation': jobLocation,
      'baseSalary': baseSalary,
      'closingDate': closingDate,
      'image': image,
      'applyUrl': applyUrl,
      'description': description,
    };
  }

  /// Helper method to get formatted location
  String get formattedLocation {
    // Clean up location string by removing extra commas and spaces
    return jobLocation
        .split(',')
        .map((part) => part.trim())
        .where((part) => part.isNotEmpty)
        .join(', ');
  }

  /// Helper method to check if closing date is soon (within 7 days)
  bool get isClosingSoon {
    try {
      final parts = closingDate.split('-');
      if (parts.length == 3) {
        final day = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final year = int.parse(parts[2]);
        final closingDateTime = DateTime(year, month, day);
        final now = DateTime.now();
        final difference = closingDateTime.difference(now).inDays;
        return difference <= 7 && difference >= 0;
      }
    } catch (e) {
      // If date parsing fails, assume not closing soon
    }
    return false;
  }

  /// Helper method to get a short description (first 150 characters)
  String get shortDescription {
    if (description.length <= 150) {
      return description;
    }
    return '${description.substring(0, 147)}...';
  }

  /// Get job category based on title and description
  String get category {
    final titleLower = title.toLowerCase();
    final descLower = description.toLowerCase();

    // IT/Technology
    if (titleLower.contains('developer') || titleLower.contains('engineer') ||
        titleLower.contains('programmer') || titleLower.contains('software') ||
        titleLower.contains('data') || titleLower.contains('analyst') ||
        titleLower.contains('tech') || titleLower.contains('it ') ||
        titleLower.contains('automation') || titleLower.contains('devops') ||
        descLower.contains('programming') || descLower.contains('coding') ||
        descLower.contains('software') || descLower.contains('technical')) {
      return 'IT/Technology';
    }

    // Finance
    if (titleLower.contains('finance') || titleLower.contains('accounting') ||
        titleLower.contains('investment') || titleLower.contains('banking') ||
        titleLower.contains('compliance') || descLower.contains('financial') ||
        descLower.contains('investment') || descLower.contains('banking')) {
      return 'Finance';
    }

    // Healthcare
    if (titleLower.contains('health') || titleLower.contains('medical') ||
        titleLower.contains('nurse') || titleLower.contains('doctor') ||
        descLower.contains('healthcare') || descLower.contains('medical')) {
      return 'Healthcare';
    }

    // Sales/Marketing
    if (titleLower.contains('sales') || titleLower.contains('marketing') ||
        titleLower.contains('business') || descLower.contains('sales') ||
        descLower.contains('marketing') || descLower.contains('business development')) {
      return 'Sales/Marketing';
    }

    // Education
    if (titleLower.contains('teacher') || titleLower.contains('education') ||
        titleLower.contains('instructor') || descLower.contains('teaching') ||
        descLower.contains('education') || descLower.contains('training')) {
      return 'Education';
    }

    // Management
    if (titleLower.contains('manager') || titleLower.contains('director') ||
        titleLower.contains('supervisor') || titleLower.contains('lead') ||
        titleLower.contains('coordinator') || descLower.contains('management') ||
        descLower.contains('leadership')) {
      return 'Management';
    }

    // Engineering (Non-IT)
    if (titleLower.contains('civil') || titleLower.contains('mechanical') ||
        titleLower.contains('electrical') || titleLower.contains('construction') ||
        titleLower.contains('architect') || descLower.contains('construction') ||
        descLower.contains('engineering')) {
      return 'Engineering';
    }

    // Customer Service
    if (titleLower.contains('customer') || titleLower.contains('service') ||
        titleLower.contains('support') || titleLower.contains('receptionist') ||
        descLower.contains('customer service') || descLower.contains('customer support')) {
      return 'Customer Service';
    }

    // Operations/Logistics
    if (titleLower.contains('operations') || titleLower.contains('logistics') ||
        titleLower.contains('supply') || titleLower.contains('warehouse') ||
        descLower.contains('operations') || descLower.contains('logistics')) {
      return 'Operations';
    }

    // Quality Assurance
    if (titleLower.contains('qa') || titleLower.contains('quality') ||
        titleLower.contains('test') || descLower.contains('quality assurance') ||
        descLower.contains('testing')) {
      return 'Quality Assurance';
    }

    // Security
    if (titleLower.contains('security') || titleLower.contains('cyber') ||
        descLower.contains('security') || descLower.contains('cybersecurity')) {
      return 'Security';
    }

    // Default category
    return 'Other';
  }

  @override
  String toString() {
    return 'JobPosting(title: $title, organization: $organization, location: $jobLocation)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is JobPosting &&
        other.title == title &&
        other.organization == organization &&
        other.applyUrl == applyUrl;
  }

  @override
  int get hashCode {
    return title.hashCode ^ organization.hashCode ^ applyUrl.hashCode;
  }
}
