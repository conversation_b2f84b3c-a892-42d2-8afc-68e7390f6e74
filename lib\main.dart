import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'widgets/mobile_job_directory.dart';
import 'services/job_data_service.dart';
import 'services/favorites_service.dart';
import 'models/job_posting.dart';

void main() {
  runApp(const JobStackApp());
}

class JobStackApp extends StatelessWidget {
  const JobStackApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'JobStack',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
        useMaterial3: true,
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle.light,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        cardTheme: const CardThemeData(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
        ),
        inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          filled: true,
          fillColor: Colors.grey[50],
        ),
      ),
      home: const MobileJobPortal(),
    );
  }
}

class MobileJobPortal extends StatefulWidget {
  const MobileJobPortal({super.key});

  @override
  State<MobileJobPortal> createState() => _MobileJobPortalState();
}

class _MobileJobPortalState extends State<MobileJobPortal> {
  int _currentIndex = 0;
  List<JobPosting> _closingSoonJobs = [];
  bool _isLoading = true;
  final FavoritesService _favoritesService = FavoritesService();

  @override
  void initState() {
    super.initState();
    _favoritesService.initialize();
    _loadJobData();
  }

  Future<void> _loadJobData() async {
    try {
      final jobs = await JobDataService.loadJobPostings();
      setState(() {
        _closingSoonJobs = JobDataService.getJobsClosingSoon(jobs);
        _isLoading = false;
      });

      // Update favorites service with current job data
      _favoritesService.updateJobsList(jobs);
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'JobStack',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              _loadJobData();
            },
          ),
        ],
      ),
      body: IndexedStack(
        index: _currentIndex,
        children: [
          // All Jobs Tab
          const MobileJobDirectory(
            showSearchBar: true,
            showFilters: true,
          ),

          // Closing Soon Tab
          _buildClosingSoonTab(),

          // Favorites Tab (placeholder)
          _buildFavoritesTab(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Colors.blue,
        unselectedItemColor: Colors.grey,
        items: [
          const BottomNavigationBarItem(
            icon: Icon(Icons.work),
            label: 'All Jobs',
          ),
          BottomNavigationBarItem(
            icon: Stack(
              children: [
                const Icon(Icons.schedule),
                if (_closingSoonJobs.isNotEmpty)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        '${_closingSoonJobs.length}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            label: 'Closing Soon',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.favorite),
            label: 'Favorites',
          ),
        ],
      ),
    );
  }

  Widget _buildClosingSoonTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_closingSoonJobs.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.schedule,
                size: 80,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'No jobs closing soon',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Check back later for urgent opportunities',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[500],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(Icons.warning, color: Colors.orange[700]),
              const SizedBox(width: 8),
              Text(
                '${_closingSoonJobs.length} jobs closing within 7 days',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.orange[700],
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: MobileJobDirectory(
            showSearchBar: false,
            showFilters: false,
            maxItems: _closingSoonJobs.length,
          ),
        ),
      ],
    );
  }

  Widget _buildFavoritesTab() {
    return AnimatedBuilder(
      animation: _favoritesService,
      builder: (context, child) {
        final favoriteJobs = _favoritesService.favoriteJobs;

        if (favoriteJobs.isEmpty) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(32.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.favorite_border,
                    size: 80,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No favorites yet',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Tap the heart icon on jobs to save them here',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[500],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }

        return Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(Icons.favorite, color: Colors.red[600]),
                  const SizedBox(width: 8),
                  Text(
                    '${favoriteJobs.length} favorite jobs',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.red[600],
                    ),
                  ),
                  const Spacer(),
                  if (favoriteJobs.isNotEmpty)
                    TextButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            title: const Text('Clear Favorites'),
                            content: const Text('Remove all jobs from favorites?'),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.pop(context),
                                child: const Text('Cancel'),
                              ),
                              TextButton(
                                onPressed: () {
                                  _favoritesService.clearFavorites();
                                  Navigator.pop(context);
                                },
                                child: const Text('Clear All'),
                              ),
                            ],
                          ),
                        );
                      },
                      child: const Text('Clear All'),
                    ),
                ],
              ),
            ),
            Expanded(
              child: ListView.builder(
                itemCount: favoriteJobs.length,
                itemBuilder: (context, index) {
                  return MobileJobCard(
                    job: favoriteJobs[index],
                    isCompact: false,
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }
}


